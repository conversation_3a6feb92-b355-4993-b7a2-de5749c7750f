#!/system/bin/sh

# ActivityChronos Service Startup Script
# This script runs on every boot after Magisk is loaded

MODDIR=${0%/*}
BINARY_PATH="$MODDIR/bin/activity-chronos"
LOGPATH="/data/local/tmp/activity_chronos.log"

# Wait for system to be fully loaded
sleep 30

# Check if binary exists
if [ ! -f "$BINARY_PATH" ]; then
    echo "$(date): Binary not found at $BINARY_PATH" >> "$LOGPATH"
    exit 1
fi

# Make sure binary is executable
chmod 755 "$BINARY_PATH"

# Start ActivityChronos with backtrace enabled
echo "$(date): Starting ActivityChronos..." >> "$LOGPATH"
RUST_BACKTRACE=1 nohup "$BINARY_PATH" >>"$LOGPATH" 2>&1 &

# Log the PID
PID=$!
echo "$(date): ActivityChronos started with PID: $PID" >> "$LOGPATH"
