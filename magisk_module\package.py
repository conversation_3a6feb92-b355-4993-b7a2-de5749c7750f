#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Magisk模块打包脚本
使用7-Zip将magisk_module文件夹内容压缩成zip文件
"""

import os
import sys
import subprocess
import datetime
from pathlib import Path

# 7-Zip可执行文件路径
SEVEN_ZIP_PATH = r"D:\7-Zip\7z.exe"

def check_7zip():
    """检查7-Zip是否存在"""
    if not os.path.exists(SEVEN_ZIP_PATH):
        print(f"错误: 7-Zip未找到，请确认路径: {SEVEN_ZIP_PATH}")
        return False
    return True

def get_output_filename():
    """生成输出文件名，包含时间戳"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"ActivityChronos_Magisk_Module_{timestamp}.zip"

def package_module():
    """打包magisk_module文件夹内容"""
    # 获取当前脚本所在目录（magisk_module文件夹）
    script_dir = Path(__file__).parent.absolute()
    
    # 输出文件路径（在magisk_module文件夹中）
    output_file = script_dir / get_output_filename()
    
    print(f"开始打包magisk_module文件夹...")
    print(f"源目录: {script_dir}")
    print(f"输出文件: {output_file}")
    
    # 要包含的文件和文件夹
    items_to_package = [
        "bin",
        "module.prop", 
        "service.sh"
    ]
    
    # 检查所有要打包的项目是否存在
    missing_items = []
    for item in items_to_package:
        item_path = script_dir / item
        if not item_path.exists():
            missing_items.append(item)
    
    if missing_items:
        print(f"警告: 以下文件/文件夹不存在: {', '.join(missing_items)}")
        response = input("是否继续打包存在的文件? (y/n): ")
        if response.lower() != 'y':
            print("打包已取消")
            return False
    
    # 构建7-Zip命令
    cmd = [SEVEN_ZIP_PATH, "a", "-tzip", str(output_file)]
    
    # 添加要打包的项目
    for item in items_to_package:
        item_path = script_dir / item
        if item_path.exists():
            cmd.append(str(item))
    
    try:
        # 切换到magisk_module目录执行命令
        result = subprocess.run(
            cmd,
            cwd=script_dir,
            capture_output=True,
            text=True,
            check=True
        )
        
        print("打包成功!")
        print(f"输出文件: {output_file}")
        print(f"文件大小: {output_file.stat().st_size / 1024:.2f} KB")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"发生错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("ActivityChronos Magisk模块打包工具")
    print("=" * 50)
    
    # 检查7-Zip
    if not check_7zip():
        sys.exit(1)
    
    # 执行打包
    success = package_module()
    
    if success:
        print("\n打包完成!")
    else:
        print("\n打包失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
